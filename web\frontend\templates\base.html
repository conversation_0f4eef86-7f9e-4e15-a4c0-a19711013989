<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Web Scraper Dashboard{% endblock %}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="/static/css/dashboard.css" rel="stylesheet">

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/app">
                <i class="bi bi-robot"></i>
                Web Scraper Pro
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/app" id="nav-dashboard">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/app/jobs" id="nav-jobs">
                            <i class="bi bi-list-task"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/app/agents" id="nav-agents">
                            <i class="bi bi-cpu"></i> Agents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/app/monitoring" id="nav-monitoring">
                            <i class="bi bi-graph-up"></i> Monitoring
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/app/data" id="nav-data">
                            <i class="bi bi-database"></i> Data
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> User
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-question-circle"></i> Help</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar (optional for larger screens) -->
            <nav class="col-md-2 d-none d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Quick Actions</span>
                    </h6>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="createNewJob()">
                                <i class="bi bi-plus-circle"></i> New Job
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="viewSystemStatus()">
                                <i class="bi bi-activity"></i> System Status
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="exportData()">
                                <i class="bi bi-download"></i> Export Data
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>System Status</span>
                    </h6>
                    <div class="px-3">
                        <div class="status-indicator mb-2">
                            <span class="badge bg-success" id="system-status">Online</span>
                        </div>
                        <div class="small text-muted">
                            <div>Active Jobs: <span id="active-jobs-count">0</span></div>
                            <div>Agents: <span id="active-agents-count">0</span></div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main content area -->
            <main class="col-md-10 ms-sm-auto px-md-4">
                <!-- Breadcrumb -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}Dashboard{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>

                <!-- Alert area for notifications -->
                <div id="alert-container"></div>

                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Unified Configuration (if available) -->
    <script src="/static/js/unified_config.js"></script>

    <!-- Custom JS -->
    <script src="/static/js/common.js"></script>
    <script src="/static/js/websocket.js"></script>
    
    {% block extra_scripts %}{% endblock %}

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            initializeWebSocket();
            updateSystemStatus();
        });
    </script>
</body>
</html>
